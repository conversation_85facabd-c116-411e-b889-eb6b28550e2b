local UseHardDebug = Config.HardDebug

local itemPool = {
    [1] = "nitrous_install_kit",
    [2] = "nitrous_bottle",
    [3] = "fakeplate",
    [4] = "lighting_controller",
    [5] = "duct_tape",
    [6] = "engine_oil",
    [7] = "tyre_replacement",
    [8] = "clutch_replacement",
    [9] = "air_filter",
    [10] = "spark_plug",
    [11] = "brakepad_replacement",
    [12] = "suspension_parts",
    [13] = "awd_drivetrain",
    [14] = "rwd_drivetrain",
    [15] = "fwd_drivetrain",
    [16] = "slick_tyres",
    [17] = "semi_slick_tyres",
    [18] = "offroad_tyres",
    [19] = "drift_tuning_kit",
    [20] = "r488sound",
    [21] = "k20a",
    [22] = "urusv8",
    [23] = "m297zonda",
    [24] = "v8engine",
    [25] = "shonen",
    [26] = "predatorv8",
    [27] = "gt3flat6",
    [28] = "lambov10",
    [29] = "rotary7",
    [30] = "supra2jzgtett",
    [31] = "m158huayra",
    [32] = "viperv10",
    [33] = "veyronsound",
    [34] = "perfov10",
    [35] = "sestov10",
    [36] = "mclarenv8",
    [37] = "murciev12",
    [38] = "r35sound",
    [39] = "musv8",
    [40] = "apollosv8",
    [41] = "avesvv12",
    [42] = "diablov12",
    [43] = "f40v8",
    [44] = "f50v12",
    [45] = "ferrarif12",
    [46] = "gtaspanov10",
    [47] = "crypto-converter",
}

function ChanceForItem(playerId, Player)
    -- Hard Debug: Item reward attempt
    if UseHardDebug then
        print('^1========================================^0')
        print('^1         ITEM REWARD DEBUG            ^0')
        print('^1========================================^0')
        print('^3Player ID:^0 ' .. tostring(playerId))
        print('^3Player Name:^0 ' .. tostring(GetPlayerName(playerId)))
        print('^3Citizen ID:^0 ' .. tostring(Player and Player.PlayerData and Player.PlayerData.citizenid or 'Unknown'))
    end

    local chance = math.random(1, 15)
    if UseHardDebug then
        print('^3Chance Roll:^0 ' .. tostring(chance) .. '/15 (need >= 9)')
    end

    if chance >= 9 then
        local Item = math.random(1, 100)
        if UseHardDebug then
            print('^2Item chance passed! Rolling for item type...^0')
            print('^3Item Roll:^0 ' .. tostring(Item) .. '/100')
        end

        if Item <= 10 then
            local metadata = { description = string.format('Uses: %s', "100"), uses = 100}
            exports.ox_inventory:AddItem(playerId, itemPool[1], 1, metadata)
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "red", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemPool[1])

            if UseHardDebug then
                print('^6RARE ITEM AWARDED:^0 ' .. tostring(itemPool[1]))
                print('^6Metadata:^0 Uses: 100')
                print('^1========================================^0')
            end
            return
        elseif Item <= 100 then
            local itemToGive = itemPool[math.random(2, #itemPool-1)]
            if itemToGive == itemPool[3] then
                exports.ox_inventory:AddItem(playerId, itemToGive, 1)
            else
                exports.ox_inventory:AddItem(playerId, itemToGive, 1)
            end
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "red", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemToGive)

            if UseHardDebug then
                print('^6ITEM AWARDED:^0 ' .. tostring(itemToGive))
                print('^1========================================^0')
            end
            return
        end
    else
        if UseHardDebug then
            print('^1No item reward (chance failed)^0')
            print('^1========================================^0')
        end
    end
end

function ChanceForCrypto(playerId, Player)
    -- Hard Debug: Crypto reward attempt
    if UseHardDebug then
        print('^1========================================^0')
        print('^1        CRYPTO REWARD DEBUG           ^0')
        print('^1========================================^0')
        print('^3Player ID:^0 ' .. tostring(playerId))
        print('^3Player Name:^0 ' .. tostring(GetPlayerName(playerId)))
        print('^3Citizen ID:^0 ' .. tostring(Player and Player.PlayerData and Player.PlayerData.citizenid or 'Unknown'))
    end

    local chance = math.random(1, 15)
    if UseHardDebug then
        print('^3Chance Roll:^0 ' .. tostring(chance) .. '/15 (need >= 8)')
    end

    if chance >= 8 then
       local Item = math.random(1, 100)
       if UseHardDebug then
           print('^2Crypto chance passed! Rolling for crypto award...^0')
           print('^3Crypto Roll:^0 ' .. tostring(Item) .. '/100 (need <= 65)')
       end

       if Item <= 65 then
            exports.ox_inventory:AddItem(playerId, itemPool[47], 1)
            Wait(100)
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "orange", "**Player** " .. GetPlayerName(playerId) .. " (citizenid: *" .. Player.PlayerData.citizenid .. "*)\n**Retrieved: "..itemPool[47])

            if UseHardDebug then
                print('^6CRYPTO CONVERTER AWARDED:^0 ' .. tostring(itemPool[47]))
                print('^1========================================^0')
            end
            return
       else
           if UseHardDebug then
               print('^1No crypto reward (item roll failed)^0')
               print('^1========================================^0')
           end
       end
    else
        if UseHardDebug then
            print('^1No crypto reward (chance failed)^0')
            print('^1========================================^0')
        end
    end
end

function addincome(cid, crypto)
    local data = MySQL.query.await('SELECT EXISTS(SELECT 1 FROM player_dailyincome WHERE cid = ? ) as exist', {cid})
    if data[1].exist == 1 then
        MySQL.query('UPDATE player_dailyincome SET crypto = crypto + ? WHERE cid = ?', {crypto, cid})
    else
        MySQL.query('INSERT INTO player_dailyincome (cid, crypto) VALUES (?, ?)', {cid, crypto})
    end
end

function checkincome(cid, src)
    local vipRole = 1372897098386640936
    local hasVipRole = exports["no-core"]:UserHasRole(src, vipRole)

    local cryptoLimit = 800
    if hasVipRole then
        cryptoLimit = cryptoLimit * 1.75
        if UseDebug then print('^2Player', src, 'is VIP via Discord role check, increasing crypto limit^0') end
    end

    local data = MySQL.query.await('SELECT EXISTS(SELECT 1 FROM player_dailyincome WHERE cid = ? ) as exist', {cid})
    if data[1].exist == 1 then
        local data2 = MySQL.query.await("SELECT * FROM player_dailyincome WHERE cid = '" .. cid .. "'")
        if data2[1].crypto > cryptoLimit then
            return false
        else
            return true
        end
    else
        return true
    end
end

-- Boosting Points Reward System (Updated to match racing-server)
function giveCurated(src, racers, position, raceMoney, racerName)
    print('^2giveCurated^0', src, racers, position, raceMoney, racerName)

    -- Hard Debug: Curated reward calculation
    if UseHardDebug then
        print('^1========================================^0')
        print('^1       CURATED REWARD DEBUG           ^0')
        print('^1========================================^0')
        print('^3Player Source:^0 ' .. tostring(src))
        print('^3Racer Name:^0 ' .. tostring(racerName))
        print('^3Total Racers:^0 ' .. tostring(racers))
        print('^3Position:^0 ' .. tostring(position))
        print('^3Race Money (Buy-in):^0 $' .. tostring(raceMoney or 0))
    end

    if racers < 0 then
        if UseHardDebug then
            print('^1Invalid racer count, aborting reward^0')
            print('^1========================================^0')
        end
        return
    end

    local playerCitizenId = getCitizenId(src)
    if not playerCitizenId then
        if UseDebug then print('^1Failed to get citizen ID for player', src, '^0') end
        if UseHardDebug then
            print('^1Failed to get citizen ID, aborting reward^0')
            print('^1========================================^0')
        end
        return
    end

    if UseHardDebug then
        print('^3Citizen ID:^0 ' .. tostring(playerCitizenId))
    end

    local placePoints = {
        [1] = 3,
        [2] = 2.5,
        [3] = 2,
    }
    local multiplier = placePoints[position] or 1.66
    local hasIncomeLimit = checkincome(playerCitizenId, src)
    if not hasIncomeLimit then
        multiplier = 1
    end
    local pointsAwarded = math.max(1, math.floor(((raceMoney or 0)/100)*multiplier + 0.5))

    if UseHardDebug then
        print('^3Base Multiplier:^0 ' .. tostring(placePoints[position] or 1.66))
        print('^3Income Limit Check:^0 ' .. tostring(hasIncomeLimit and 'PASSED' or 'FAILED (reduced multiplier)'))
        print('^3Final Multiplier:^0 ' .. tostring(multiplier))
        print('^3Calculation:^0 max(1, floor(((' .. tostring(raceMoney or 0) .. '/100)*' .. tostring(multiplier) .. ' + 0.5))')
        print('^3Points Awarded:^0 ' .. tostring(pointsAwarded))
    end

    -- Check if user exists, create if not
    local success, result = pcall(function()
        return MySQL.scalar.await('SELECT crypto FROM ra_boosting_user_settings WHERE player_identifier = ?', {playerCitizenId})
    end)

    if success then
        if result then
            -- User exists, update crypto
            MySQL.query.await('UPDATE ra_boosting_user_settings SET crypto = crypto + ? WHERE player_identifier = ?', {pointsAwarded, playerCitizenId})
            if UseHardDebug then
                print('^2Database: Updated existing user crypto balance^0')
            end
        else
            TriggerClientEvent('Framework:Notify', src, 'User not found', 'error', 5000)
            if UseHardDebug then
                print('^1Database: User not found in boosting system^0')
                print('^1========================================^0')
            end
            return
            -- -- User doesn't exist, create record
            -- MySQL.query.await('INSERT INTO ra_boosting_user_settings (player_identifier, crypto, daily_income) VALUES (?, ?, 0)', {playerCitizenId, pointsAwarded})
        end

        addincome(playerCitizenId, pointsAwarded)
        if UseHardDebug then
            print('^2Daily income updated^0')
        end

        local Player = GetPlayer(src)
        if Player then
            TriggerEvent("nocore-logs:server:SendLog", "racing", "Racing Rewards", "green",
                string.format("**Player** %s (citizenid: *%s*)\n**Retrieved: %d Boosting coins\n**Position: %d/%d**",
                    GetPlayerName(src), Player.PlayerData.citizenid, pointsAwarded, position, racers))
            TriggerClientEvent('cw-racingapp:client:notify', src,
                string.format('You received %d boosting coins for finishing %s place!', pointsAwarded,
                    position == 1 and "1st" or position == 2 and "2nd" or position == 3 and "3rd" or position.."th"), 'success')

            if UseHardDebug then
                print('^2--- ADDITIONAL REWARDS ---^0')
                print('^3Position qualifies for item chance:^0 ' .. tostring(position >= 1 and position <= 3 and 'YES' or 'NO'))
            end

            if position >= 1 and position <= 3 then
               ChanceForItem(src, Player)
            end
            Wait(100)
            ChanceForCrypto(src, Player)

            if UseHardDebug then
                print('^2--- CURATED REWARD COMPLETE ---^0')
                print('^6Final Summary:^0')
                print('  ^5Player:^0 ' .. tostring(racerName))
                print('  ^5Position:^0 ' .. tostring(position) .. '/' .. tostring(racers))
                print('  ^5Boosting Points:^0 ' .. tostring(pointsAwarded))
                print('  ^5Notification Sent:^0 YES')
                print('^1========================================^0')
            end
        else
            if UseHardDebug then
                print('^1Failed to get Player object^0')
                print('^1========================================^0')
            end
        end
    else
        if UseDebug then print('^1Database error in giveCurated for player', src, '^0') end
        TriggerClientEvent('cw-racingapp:client:notify', src, 'Database error occurred', 'error')
        if UseHardDebug then
            print('^1Database error occurred during curated reward^0')
            print('^1========================================^0')
        end
    end
    Wait(250)
end
